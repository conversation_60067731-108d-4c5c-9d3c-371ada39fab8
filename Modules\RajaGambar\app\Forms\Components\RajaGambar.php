<?php

namespace Modules\RajaGambar\Forms\Components;

use Filament\Forms\Components\FileUpload;
use Filament\Forms\Set;
use Filament\Forms\Get;
use Illuminate\Support\Facades\Storage;
use Modules\RajaGambar\Services\ImageProcessingService;

class RajaGambar extends FileUpload
{
    
    protected bool $enableProcessing = true;
    protected bool $enableResponsive = true;
    protected bool $enableEffects = true;
    protected bool $enableWatermark = false;
    protected array $allowedEffects = ['blur', 'sharpen'];
    protected array $responsiveSizes = [];
    protected ?string $watermarkPath = null;
    protected string $processingQuality = 'default';
    
    public static function make(string $name): static
    {
        $static = parent::make($name);
        
        return $static
            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp', 'image/gif'])
            ->maxSize(10240) // 10MB
            ->image()
            ->imageEditor()
            ->imageEditorAspectRatios([
                '16:9',
                '4:3',
                '1:1',
                '3:4',
                '9:16',
            ])
            ->directory('rajagambar')
            ->visibility('public')
            ->afterStateUpdated(function ($state) {
                if ($state) {
                    // Basic processing - just log the upload
                    logger()->info('RajaGambar: Image uploaded', ['file' => $state]);
                }
            });
    }
    
    public function enableProcessing(bool $enable = true): static
    {
        $this->enableProcessing = $enable;
        return $this;
    }
    
    public function enableResponsive(bool $enable = true): static
    {
        $this->enableResponsive = $enable;
        return $this;
    }
    
    public function enableEffects(bool $enable = true, array $effects = ['blur', 'sharpen']): static
    {
        $this->enableEffects = $enable;
        $this->allowedEffects = $effects;
        return $this;
    }
    
    public function enableWatermark(bool $enable = true, ?string $watermarkPath = null): static
    {
        $this->enableWatermark = $enable;
        $this->watermarkPath = $watermarkPath;
        return $this;
    }
    
    public function responsiveSizes(array $sizes): static
    {
        $this->responsiveSizes = $sizes;
        return $this;
    }
    
    public function processingQuality(string $quality): static
    {
        $this->processingQuality = $quality;
        return $this;
    }
    
    public function getProcessingActions(): array
    {
        // Return empty array to avoid Action initialization errors
        return [];
    }
    
    protected function processUploadedImage($state, Set $set, Get $get): void
    {
        if (!$this->enableProcessing || empty($state)) {
            return;
        }
        
        try {
            $service = app(ImageProcessingService::class);
            
            // Get file path
            $filePath = is_array($state) ? $state[0] : $state;
            $fullPath = Storage::disk('public')->path($filePath);
            
            if (!file_exists($fullPath)) {
                return;
            }
            
            // Auto-generate responsive sizes if enabled
            if ($this->enableResponsive && !empty($this->responsiveSizes)) {
                $this->generateResponsiveSizes($get, $set, $fullPath);
            }
            
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('RajaGambar: Error processing uploaded image', [
                'error' => $e->getMessage(),
                'file' => $filePath ?? 'unknown'
            ]);
        }
    }
    
    protected function generateResponsiveSizes(Get $get, Set $set, ?string $customPath = null): void
    {
        try {
            $service = app(ImageProcessingService::class);
            $state = $get($this->getName());
            
            if (empty($state)) {
                return;
            }
            
            $filePath = $customPath ?: (is_array($state) ? $state[0] : $state);
            $fullPath = $customPath ?: Storage::disk('public')->path($filePath);
            
            if (!file_exists($fullPath)) {
                return;
            }
            
            $responsiveSizes = $service->generateResponsiveSizes($fullPath);
            
            // Store responsive sizes in component state
            $set($this->getName() . '_responsive', $responsiveSizes);
            
            \Filament\Notifications\Notification::make()
                ->title('Responsive sizes generated')
                ->body(count($responsiveSizes) . ' sizes created successfully')
                ->success()
                ->send();
                
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Error generating responsive sizes')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    
    protected function applyEffect(string $effect, int $intensity, Get $get, Set $set): void
    {
        try {
            $service = app(ImageProcessingService::class);
            $state = $get($this->getName());
            
            if (empty($state)) {
                return;
            }
            
            $filePath = is_array($state) ? $state[0] : $state;
            $fullPath = Storage::disk('public')->path($filePath);
            
            if (!file_exists($fullPath)) {
                return;
            }
            
            $resultPath = match($effect) {
                'blur' => $service->blur($fullPath, $intensity),
                'sharpen' => $service->sharpen($fullPath, $intensity),
                default => throw new \InvalidArgumentException("Unsupported effect: {$effect}")
            };
            
            // Convert back to relative path for storage
            $relativePath = str_replace(Storage::disk('public')->path(''), '', $resultPath);
            $set($this->getName(), $relativePath);
            
            \Filament\Notifications\Notification::make()
                ->title(ucfirst($effect) . ' effect applied')
                ->body("Intensity: {$intensity}")
                ->success()
                ->send();
                
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Error applying ' . $effect)
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    
    protected function applyWatermark(array $data, Get $get, Set $set): void
    {
        try {
            $service = app(ImageProcessingService::class);
            $state = $get($this->getName());
            
            if (empty($state) || !$this->watermarkPath) {
                return;
            }
            
            $filePath = is_array($state) ? $state[0] : $state;
            $fullPath = Storage::disk('public')->path($filePath);
            
            if (!file_exists($fullPath)) {
                return;
            }
            
            $resultPath = $service->addWatermark(
                $fullPath,
                $this->watermarkPath,
                $data['position'],
                $data['opacity']
            );
            
            // Convert back to relative path for storage
            $relativePath = str_replace(Storage::disk('public')->path(''), '', $resultPath);
            $set($this->getName(), $relativePath);
            
            \Filament\Notifications\Notification::make()
                ->title('Watermark applied')
                ->body("Position: {$data['position']}, Opacity: {$data['opacity']}%")
                ->success()
                ->send();
                
        } catch (\Exception $e) {
            \Filament\Notifications\Notification::make()
                ->title('Error applying watermark')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }
    
    public function getEnableProcessing(): bool
    {
        return $this->enableProcessing;
    }
    
    public function getEnableResponsive(): bool
    {
        return $this->enableResponsive;
    }
    
    public function getEnableEffects(): bool
    {
        return $this->enableEffects;
    }
    
    public function getEnableWatermark(): bool
    {
        return $this->enableWatermark;
    }
    
    public function getAllowedEffects(): array
    {
        return $this->allowedEffects;
    }
}
