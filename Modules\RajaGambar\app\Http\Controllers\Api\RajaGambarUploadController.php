<?php

namespace Modules\RajaGambar\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\RajaGambar\Services\ImageProcessingService;

class RajaGambarUploadController extends Controller
{
    protected ImageProcessingService $imageService;
    
    public function __construct(ImageProcessingService $imageService)
    {
        $this->imageService = $imageService;
    }
    
    /**
     * Upload image file
     */
    public function upload(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|image|max:10240', // 10MB max
            'directory' => 'string|nullable',
            'visibility' => 'string|in:public,private',
            'replace' => 'string|nullable', // Path to replace existing file
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $file = $request->file('file');
            $directory = $request->input('directory', 'rajagambar');
            $visibility = $request->input('visibility', 'public');
            $replaceFile = $request->input('replace');
            
            // Generate unique filename
            $extension = $file->getClientOriginalExtension();
            $filename = Str::uuid() . '.' . $extension;
            $path = $directory . '/' . $filename;
            
            // Delete old file if replacing
            if ($replaceFile && Storage::disk('public')->exists($replaceFile)) {
                Storage::disk('public')->delete($replaceFile);
            }
            
            // Store the file
            $storedPath = $file->storeAs($directory, $filename, 'public');
            
            // Set visibility
            if ($visibility === 'private') {
                Storage::disk('public')->setVisibility($storedPath, 'private');
            }
            
            // Get file info
            $fileSize = Storage::disk('public')->size($storedPath);
            $mimeType = Storage::disk('public')->mimeType($storedPath);
            
            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => [
                    'path' => $storedPath,
                    'url' => Storage::disk('public')->url($storedPath),
                    'filename' => $filename,
                    'original_name' => $file->getClientOriginalName(),
                    'size' => $fileSize,
                    'mime_type' => $mimeType,
                    'directory' => $directory,
                    'visibility' => $visibility
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get file information
     */
    public function fileInfo(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Path is required',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $path = $request->input('path');
            
            if (!Storage::disk('public')->exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }
            
            $size = Storage::disk('public')->size($path);
            $mimeType = Storage::disk('public')->mimeType($path);
            $lastModified = Storage::disk('public')->lastModified($path);
            
            return response()->json([
                'success' => true,
                'data' => [
                    'path' => $path,
                    'url' => Storage::disk('public')->url($path),
                    'size' => $size,
                    'mime_type' => $mimeType,
                    'last_modified' => $lastModified,
                    'exists' => true
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error getting file info: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Delete file
     */
    public function delete(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Path is required',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $path = $request->input('path');
            
            if (!Storage::disk('public')->exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }
            
            Storage::disk('public')->delete($path);
            
            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting file: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Process image with effects
     */
    public function processImage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'effect' => 'required|string|in:blur,sharpen,resize,crop,rotate,flip',
            'options' => 'array'
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }
        
        try {
            $path = $request->input('path');
            $effect = $request->input('effect');
            $options = $request->input('options', []);
            
            if (!Storage::disk('public')->exists($path)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }
            
            $fullPath = Storage::disk('public')->path($path);
            $resultPath = null;
            
            switch ($effect) {
                case 'blur':
                    $intensity = $options['intensity'] ?? 5;
                    $resultPath = $this->imageService->blur($fullPath, $intensity);
                    break;
                    
                case 'sharpen':
                    $intensity = $options['intensity'] ?? 5;
                    $resultPath = $this->imageService->sharpen($fullPath, $intensity);
                    break;
                    
                case 'resize':
                    $width = $options['width'] ?? null;
                    $height = $options['height'] ?? null;
                    if ($width || $height) {
                        $resultPath = $this->imageService->resize($fullPath, $width, $height);
                    }
                    break;
                    
                default:
                    throw new \InvalidArgumentException("Unsupported effect: {$effect}");
            }
            
            if ($resultPath) {
                $relativePath = str_replace(Storage::disk('public')->path(''), '', $resultPath);
                
                return response()->json([
                    'success' => true,
                    'message' => 'Image processed successfully',
                    'data' => [
                        'path' => $relativePath,
                        'url' => Storage::disk('public')->url($relativePath),
                        'effect' => $effect,
                        'options' => $options
                    ]
                ]);
            }
            
            throw new \Exception('Processing failed');
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Processing failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
