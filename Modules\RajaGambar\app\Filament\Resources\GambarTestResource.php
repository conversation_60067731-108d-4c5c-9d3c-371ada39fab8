<?php

namespace Modules\RajaGambar\app\Filament\Resources;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Modules\RajaGambar\app\Filament\Resources\GambarTestResource\Pages;
use Modules\RajaGambar\app\Filament\Resources\GambarTestResource\RelationManagers;
use Modules\RajaGambar\app\Models\GambarTest;
use Modules\RajaGambar\app\Forms\Components\RajaGambarUpload;

class GambarTestResource extends Resource
{
    protected static ?string $model = GambarTest::class;

    protected static ?string $navigationIcon = 'heroicon-o-photo';

    protected static ?string $navigationLabel = 'Test RajaGambarUpload';

    protected static ?string $modelLabel = 'Gambar Test';

    protected static ?string $pluralModelLabel = 'Gambar Test';

    protected static ?string $navigationGroup = 'Raja Gambar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('title')
                    ->label('Judul')
                    ->required()
                    ->maxLength(255),
                
                RajaGambarUpload::make('featured_image')
                    ->label('Gambar Utama')
                    ->placeholder('Upload gambar utama')
                    ->previewSize(300)
                    ->maxFileSize(5)
                    ->acceptedTypes('image/jpeg,image/png,image/webp,image/gif')
                    ->directory('test/featured')
                    ->visibility('public')
                    ->editorTheme([
                        'common.backgroundColor' => '#f5f5f5',
                        'common.border' => '1px solid #ddd'
                    ])
                    ->enableEditor()
                    ->enableResize()
                    ->enableCrop()
                    ->enableRotate()
                    ->enableFlip()
                    ->enableFilter()
                    ->enableText()
                    ->enableDraw()
                    ->enableShape(),
                
                Forms\Components\Textarea::make('description')
                    ->label('Deskripsi')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Judul')
                    ->searchable(),
                
                Tables\Columns\ImageColumn::make('featured_image')
                    ->label('Gambar')
                    ->size(60),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Diperbarui')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGambarTests::route('/'),
            'create' => Pages\CreateGambarTest::route('/create'),
            'edit' => Pages\EditGambarTest::route('/{record}/edit'),
        ];
    }
}
