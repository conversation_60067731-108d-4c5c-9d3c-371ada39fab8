{{--
  RajaGambarUpload Component with TUI Image Editor
  
  Features:
  - Upload gambar dengan drag & drop
  - Editor gambar menggunakan tui.image-editor
  - Resize, crop, rotate, flip, filter, text, draw, shape
  - Preview gambar yang sudah diedit
  - Integrasi dengan Filament Forms
--}}

@php
  $id = $getId();
  $statePath = $getStatePath();
  $state = $getState();
  $placeholder = $getPlaceholder();
  $previewSize = $getPreviewSize();
  $maxFileSize = $getMaxFileSize();
  $acceptedTypes = $getAcceptedFileTypesString();
  $directory = $getDirectory();
  $visibility = $getVisibility();
  $editorTheme = $getEditorTheme();
  $editorOptions = $getEditorOptions();
  
  // Editor capabilities
  $enableEditor = $isEditorEnabled();
  $enableResize = $isResizeEnabled();
  $enableCrop = $isCropEnabled();
  $enableRotate = $isRotateEnabled();
  $enableFlip = $isFlipEnabled();
  $enableFilter = $isFilterEnabled();
  $enableText = $isTextEnabled();
  $enableDraw = $isDrawEnabled();
  $enableShape = $isShapeEnabled();
@endphp

<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">
  <div class="raja-gambar-upload-container" x-data="rajaGambarUpload({
      statePath: '{{ $statePath }}',
      placeholder: '{{ $placeholder }}',
      previewSize: {{ $previewSize }},
      maxFileSize: {{ $maxFileSize }},
      acceptedTypes: '{{ $acceptedTypes }}',
      directory: '{{ $directory }}',
      visibility: '{{ $visibility }}',
      editorTheme: @js($editorTheme),
      editorOptions: @js($editorOptions),
      enableEditor: {{ $enableEditor ? 'true' : 'false' }},
      enableResize: {{ $enableResize ? 'true' : 'false' }},
      enableCrop: {{ $enableCrop ? 'true' : 'false' }},
      enableRotate: {{ $enableRotate ? 'true' : 'false' }},
      enableFlip: {{ $enableFlip ? 'true' : 'false' }},
      enableFilter: {{ $enableFilter ? 'true' : 'false' }},
      enableText: {{ $enableText ? 'true' : 'false' }},
      enableDraw: {{ $enableDraw ? 'true' : 'false' }},
      enableShape: {{ $enableShape ? 'true' : 'false' }},
      currentValue: @js($state)
  })">
    
    <!-- Hidden input untuk menyimpan nilai -->
    <input
      {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
      name="{{ $statePath }}" type="hidden" x-model="value" />
    
    <!-- Upload Area -->
    <div class="raja-gambar-upload-area" x-show="!hasImage">
      <div 
        class="upload-dropzone"
        @click="triggerFileInput()"
        @dragover.prevent="dragOver = true"
        @dragleave.prevent="dragOver = false"
        @drop.prevent="handleFileDrop($event)"
        :class="{ 'drag-over': dragOver }"
      >
        <div class="upload-content">
          <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          <p class="upload-text" x-text="placeholder"></p>
          <p class="upload-hint">Atau drag & drop gambar di sini</p>
          <p class="upload-info">
            Maksimal {{ $maxFileSize }}MB - Format: {{ str_replace(['image/', ','], ['', ', '], $acceptedTypes) }}
          </p>
        </div>
      </div>
      
      <!-- Hidden file input -->
      <input 
        type="file" 
        x-ref="fileInput"
        @change="handleFileSelect($event)"
        :accept="acceptedTypes"
        class="hidden"
      />
    </div>
    
    <!-- Preview & Editor Area -->
    <div class="raja-gambar-preview-area" x-show="hasImage">
      <!-- Current Image Preview -->
      <div class="current-image-preview" x-show="!showEditor">
        <div class="image-container">
          <img 
            x-ref="previewImage"
            :src="imagePreviewUrl" 
            :alt="fileName"
            :style="`max-width: ${previewSize}px; max-height: ${previewSize}px;`"
            class="preview-image"
          />
        </div>
        
        <div class="image-actions">
          <button 
            type="button"
            @click="openEditor()"
            class="btn-edit"
            x-show="enableEditor"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit Gambar
          </button>
          
          <button 
            type="button"
            @click="removeImage()"
            class="btn-remove"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Hapus
          </button>
        </div>
        
        <div class="image-info" x-show="fileName">
          <p class="file-name" x-text="fileName"></p>
          <p class="file-size" x-text="fileSize"></p>
        </div>
      </div>
      
      <!-- TUI Image Editor -->
      <div class="tui-image-editor-container" x-show="showEditor">
        <div class="editor-header">
          <h3 class="editor-title">Edit Gambar</h3>
          <div class="editor-actions">
            <button 
              type="button"
              @click="saveEditedImage()"
              class="btn-save"
            >
              Simpan
            </button>
            <button 
              type="button"
              @click="cancelEdit()"
              class="btn-cancel"
            >
              Batal
            </button>
          </div>
        </div>
        
        <div 
          x-ref="tuiImageEditor"
          class="tui-image-editor"
        ></div>
      </div>
    </div>
    
    <!-- Loading State -->
    <div class="loading-overlay" x-show="loading">
      <div class="loading-spinner">
        <svg class="animate-spin h-8 w-8" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p>Memproses gambar...</p>
      </div>
    </div>
  </div>
</x-dynamic-component>

@pushOnce('styles')
<style>
.raja-gambar-upload-container {
  position: relative;
}

.raja-gambar-upload-area {
  margin-bottom: 1rem;
}

.upload-dropzone {
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background-color: #f9fafb;
}

.upload-dropzone:hover,
.upload-dropzone.drag-over {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.upload-icon {
  width: 3rem;
  height: 3rem;
  color: #6b7280;
}

.upload-text {
  font-size: 1.125rem;
  font-weight: 500;
  color: #374151;
  margin: 0;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.upload-info {
  font-size: 0.75rem;
  color: #9ca3af;
  margin: 0;
}

.raja-gambar-preview-area {
  margin-bottom: 1rem;
}

.current-image-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.image-container {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.preview-image {
  display: block;
  border-radius: 0.5rem;
  object-fit: cover;
}

.image-actions {
  display: flex;
  gap: 0.5rem;
}

.btn-edit,
.btn-remove,
.btn-save,
.btn-cancel {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  border: none;
  cursor: pointer;
}

.btn-edit {
  background-color: #3b82f6;
  color: white;
}

.btn-edit:hover {
  background-color: #2563eb;
}

.btn-remove {
  background-color: #ef4444;
  color: white;
}

.btn-remove:hover {
  background-color: #dc2626;
}

.btn-save {
  background-color: #10b981;
  color: white;
}

.btn-save:hover {
  background-color: #059669;
}

.btn-cancel {
  background-color: #6b7280;
  color: white;
}

.btn-cancel:hover {
  background-color: #4b5563;
}

.image-info {
  text-align: center;
}

.file-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin: 0 0 0.25rem 0;
}

.file-size {
  font-size: 0.75rem;
  color: #6b7280;
  margin: 0;
}

.tui-image-editor-container {
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  overflow: hidden;
  background-color: white;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.editor-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.editor-actions {
  display: flex;
  gap: 0.5rem;
}

.tui-image-editor {
  height: 500px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  z-index: 10;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #3b82f6;
}

.loading-spinner p {
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

/* TUI Image Editor custom styles */
.tui-image-editor-canvas-container {
  background-color: #f5f5f5 !important;
}

.tui-image-editor-menu {
  background-color: #ffffff !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.tui-image-editor-submenu {
  background-color: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
}

/* Responsive */
@media (max-width: 640px) {
  .upload-dropzone {
    padding: 1.5rem 1rem;
  }

  .upload-icon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .upload-text {
    font-size: 1rem;
  }

  .image-actions {
    flex-direction: column;
    width: 100%;
  }

  .btn-edit,
  .btn-remove,
  .btn-save,
  .btn-cancel {
    justify-content: center;
    width: 100%;
  }

  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .editor-actions {
    justify-content: center;
  }

  .tui-image-editor {
    height: 400px;
  }
}
</style>
@endPushOnce

@pushOnce('scripts')
<script>
document.addEventListener('alpine:init', () => {
  Alpine.data('rajaGambarUpload', (config) => ({
    // Configuration
    statePath: config.statePath,
    placeholder: config.placeholder,
    previewSize: config.previewSize,
    maxFileSize: config.maxFileSize,
    acceptedTypes: config.acceptedTypes,
    directory: config.directory,
    visibility: config.visibility,
    editorTheme: config.editorTheme,
    editorOptions: config.editorOptions,
    enableEditor: config.enableEditor,
    enableResize: config.enableResize,
    enableCrop: config.enableCrop,
    enableRotate: config.enableRotate,
    enableFlip: config.enableFlip,
    enableFilter: config.enableFilter,
    enableText: config.enableText,
    enableDraw: config.enableDraw,
    enableShape: config.enableShape,

    // State
    value: config.currentValue || null,
    hasImage: false,
    imagePreviewUrl: null,
    fileName: null,
    fileSize: null,
    dragOver: false,
    loading: false,
    showEditor: false,

    // TUI Image Editor instance
    imageEditor: null,
    originalImageData: null,

    // Initialize
    init() {
      this.loadCurrentImage();

      // Watch for value changes
      this.$watch('value', (newValue) => {
        this.syncWithLivewire(newValue);
        this.loadCurrentImage();
      });
    },

    // Load current image if exists
    async loadCurrentImage() {
      if (this.value) {
        this.hasImage = true;
        this.imagePreviewUrl = this.getImageUrl(this.value);

        // Extract file info from path
        const pathParts = this.value.split('/');
        this.fileName = pathParts[pathParts.length - 1];

        try {
          // Get file size info
          const response = await fetch(`/api/rajagambar/file-info?path=${encodeURIComponent(this.value)}`);
          if (response.ok) {
            const data = await response.json();
            this.fileSize = this.formatFileSize(data.size);
          }
        } catch (error) {
          console.warn('Could not load file info:', error);
        }
      } else {
        this.hasImage = false;
        this.imagePreviewUrl = null;
        this.fileName = null;
        this.fileSize = null;
      }
    },

    // Get full image URL
    getImageUrl(path) {
      if (!path) return null;
      if (path.startsWith('http')) return path;
      return `/storage/${path}`;
    },

    // Format file size
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // Trigger file input
    triggerFileInput() {
      this.$refs.fileInput.click();
    },

    // Handle file selection
    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.processFile(file);
      }
    },

    // Handle file drop
    handleFileDrop(event) {
      this.dragOver = false;
      const file = event.dataTransfer.files[0];
      if (file) {
        this.processFile(file);
      }
    },

    // Process uploaded file
    async processFile(file) {
      // Validate file type
      if (!this.acceptedTypes.split(',').some(type => file.type === type.trim())) {
        this.showNotification('Tipe file tidak didukung', 'error');
        return;
      }

      // Validate file size
      if (file.size > this.maxFileSize * 1024 * 1024) {
        this.showNotification(`Ukuran file terlalu besar. Maksimal ${this.maxFileSize}MB`, 'error');
        return;
      }

      this.loading = true;

      try {
        // Upload file
        const formData = new FormData();
        formData.append('file', file);
        formData.append('directory', this.directory);
        formData.append('visibility', this.visibility);

        const response = await fetch('/api/rajagambar/upload', {
          method: 'POST',
          body: formData,
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        });

        if (response.ok) {
          const data = await response.json();
          this.value = data.path;
          this.fileName = file.name;
          this.fileSize = this.formatFileSize(file.size);
          this.hasImage = true;
          this.imagePreviewUrl = this.getImageUrl(data.path);

          this.showNotification('Gambar berhasil diupload', 'success');
        } else {
          throw new Error('Upload failed');
        }
      } catch (error) {
        console.error('Upload error:', error);
        this.showNotification('Gagal mengupload gambar', 'error');
      } finally {
        this.loading = false;
      }
    },

    // Remove image
    removeImage() {
      this.value = null;
      this.hasImage = false;
      this.imagePreviewUrl = null;
      this.fileName = null;
      this.fileSize = null;
      this.showEditor = false;

      // Reset file input
      this.$refs.fileInput.value = '';

      this.showNotification('Gambar dihapus', 'info');
    },

    // Open TUI Image Editor
    async openEditor() {
      if (!this.hasImage || !this.enableEditor) return;

      this.loading = true;
      this.showEditor = true;

      try {
        // Wait for DOM to update
        await this.$nextTick();

        // Initialize TUI Image Editor
        await this.initializeImageEditor();
      } catch (error) {
        console.error('Error opening editor:', error);
        this.showNotification('Gagal membuka editor', 'error');
        this.showEditor = false;
      } finally {
        this.loading = false;
      }
    },

    // Initialize TUI Image Editor
    async initializeImageEditor() {
      if (this.imageEditor) {
        this.imageEditor.destroy();
      }

      // Import TUI Image Editor from CDN (since local import might not work)
      if (!window.tui || !window.tui.ImageEditor) {
        // Load TUI Image Editor from CDN
        await this.loadTuiImageEditor();
      }

      const ImageEditor = window.tui.ImageEditor;

      // Configure editor options
      const editorOptions = {
        includeUI: {
          loadImage: {
            path: this.imagePreviewUrl,
            name: this.fileName
          },
          theme: this.editorTheme,
          menu: this.getMenuConfig(),
          initMenu: 'filter',
          uiSize: {
            width: '100%',
            height: '500px'
          },
          menuBarPosition: 'bottom'
        },
        cssMaxWidth: 700,
        cssMaxHeight: 500,
        usageStatistics: false,
        ...this.editorOptions
      };

      // Initialize editor
      this.imageEditor = new ImageEditor(this.$refs.tuiImageEditor, editorOptions);

      // Store original image data
      this.originalImageData = this.imageEditor.toDataURL();
    },

    // Load TUI Image Editor from CDN
    async loadTuiImageEditor() {
      return new Promise((resolve, reject) => {
        // Load CSS first
        if (!document.querySelector('link[href*="tui-image-editor"]')) {
          const cssLink = document.createElement('link');
          cssLink.rel = 'stylesheet';
          cssLink.href = 'https://uicdn.toast.com/tui-image-editor/latest/tui-image-editor.css';
          document.head.appendChild(cssLink);
        }

        // Load Fabric.js (required by TUI Image Editor)
        if (!window.fabric) {
          const fabricScript = document.createElement('script');
          fabricScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/fabric.js/4.6.0/fabric.min.js';
          fabricScript.onload = () => {
            // Load TUI Image Editor
            const tuiScript = document.createElement('script');
            tuiScript.src = 'https://uicdn.toast.com/tui-image-editor/latest/tui-image-editor.js';
            tuiScript.onload = () => resolve();
            tuiScript.onerror = () => reject(new Error('Failed to load TUI Image Editor'));
            document.head.appendChild(tuiScript);
          };
          fabricScript.onerror = () => reject(new Error('Failed to load Fabric.js'));
          document.head.appendChild(fabricScript);
        } else if (!window.tui || !window.tui.ImageEditor) {
          // Fabric.js already loaded, just load TUI Image Editor
          const tuiScript = document.createElement('script');
          tuiScript.src = 'https://uicdn.toast.com/tui-image-editor/latest/tui-image-editor.js';
          tuiScript.onload = () => resolve();
          tuiScript.onerror = () => reject(new Error('Failed to load TUI Image Editor'));
          document.head.appendChild(tuiScript);
        } else {
          // Already loaded
          resolve();
        }
      });
    },

    // Get menu configuration based on enabled features
    getMenuConfig() {
      const menu = [];

      if (this.enableCrop) menu.push('crop');
      if (this.enableFlip) menu.push('flip');
      if (this.enableRotate) menu.push('rotate');
      if (this.enableDraw) menu.push('draw');
      if (this.enableShape) menu.push('shape');
      if (this.enableText) menu.push('text');
      if (this.enableFilter) menu.push('filter');

      return menu;
    },

    // Save edited image
    async saveEditedImage() {
      if (!this.imageEditor) return;

      this.loading = true;

      try {
        // Get edited image data
        const editedImageData = this.imageEditor.toDataURL();

        // Convert data URL to blob
        const response = await fetch(editedImageData);
        const blob = await response.blob();

        // Create form data
        const formData = new FormData();
        formData.append('file', blob, this.fileName);
        formData.append('directory', this.directory);
        formData.append('visibility', this.visibility);
        formData.append('replace', this.value); // Replace existing file

        // Upload edited image
        const uploadResponse = await fetch('/api/rajagambar/upload', {
          method: 'POST',
          body: formData,
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        });

        if (uploadResponse.ok) {
          const data = await uploadResponse.json();
          this.value = data.path;
          this.imagePreviewUrl = this.getImageUrl(data.path);

          this.showNotification('Gambar berhasil disimpan', 'success');
          this.closeEditor();
        } else {
          throw new Error('Save failed');
        }
      } catch (error) {
        console.error('Save error:', error);
        this.showNotification('Gagal menyimpan gambar', 'error');
      } finally {
        this.loading = false;
      }
    },

    // Cancel edit
    cancelEdit() {
      this.closeEditor();
    },

    // Close editor
    closeEditor() {
      this.showEditor = false;
      if (this.imageEditor) {
        this.imageEditor.destroy();
        this.imageEditor = null;
      }
      this.originalImageData = null;
    },

    // Sync with Livewire
    syncWithLivewire(value) {
      // Update Livewire component
      if (window.Livewire) {
        this.$wire.set(this.statePath, value);
      }
    },

    // Show notification
    showNotification(message, type = 'info') {
      // Use Filament's notification system if available
      if (window.$wire && window.$wire.notify) {
        window.$wire.notify(message, type);
      } else {
        // Fallback to console
        console.log(`[${type.toUpperCase()}] ${message}`);
      }
    },

    // Cleanup when component is destroyed
    destroy() {
      if (this.imageEditor) {
        this.imageEditor.destroy();
      }
    }
  }));
});
</script>
@endPushOnce
