<?php

/**
 * <PERSON><PERSON><PERSON> penggunaan <PERSON>ambarUpload Field Component
 * 
 * RajaGambarUpload adalah custom field untuk upload gambar dengan editor
 * menggunakan TUI Image Editor yang tidak extends FileUpload.
 * 
 * Fitur:
 * - Upload gambar dengan drag & drop
 * - Editor gambar dengan TUI Image Editor
 * - Resize, crop, rotate, flip, filter, text, draw, shape
 * - Preview gambar yang sudah diedit
 * - Integrasi dengan Filament Forms
 */

use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

// Contoh penggunaan dasar
RajaGambarUpload::make('featured_image')
    ->label('Gambar Utama')
    ->placeholder('Upload gambar utama artikel')
    ->directory('articles/featured')
    ->maxFileSize(5) // 5MB
    ->previewSize(400);

// Contoh dengan konfigurasi lengkap
RajaGambarUpload::make('profile_picture')
    ->label('Foto Profil')
    ->placeholder('Upload foto profil Anda')
    ->directory('profiles')
    ->visibility('public')
    ->acceptedFileTypes(['image/jpeg', 'image/png'])
    ->maxFileSize(2) // 2MB
    ->previewSize(300)
    ->enableEditor(true)
    ->enableResize(true)
    ->enableCrop(true)
    ->enableRotate(true)
    ->enableFlip(true)
    ->enableFilter(true)
    ->enableText(false) // Disable text tool
    ->enableDraw(false) // Disable draw tool
    ->enableShape(false) // Disable shape tool
    ->editorTheme([
        'common.backgroundColor' => '#ffffff',
        'common.border' => '1px solid #e5e7eb',
    ]);

// Contoh untuk galeri produk
RajaGambarUpload::make('product_images')
    ->label('Gambar Produk')
    ->placeholder('Upload gambar produk')
    ->directory('products')
    ->maxFileSize(10) // 10MB
    ->previewSize(250)
    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
    ->editorOptions([
        'cssMaxWidth' => 800,
        'cssMaxHeight' => 600,
    ]);

// Contoh dengan editor minimal (hanya crop dan resize)
RajaGambarUpload::make('thumbnail')
    ->label('Thumbnail')
    ->placeholder('Upload thumbnail')
    ->directory('thumbnails')
    ->maxFileSize(1) // 1MB
    ->previewSize(200)
    ->enableEditor(true)
    ->enableResize(true)
    ->enableCrop(true)
    ->enableRotate(false)
    ->enableFlip(false)
    ->enableFilter(false)
    ->enableText(false)
    ->enableDraw(false)
    ->enableShape(false);

// Contoh dengan tema custom
RajaGambarUpload::make('banner_image')
    ->label('Banner')
    ->placeholder('Upload banner image')
    ->directory('banners')
    ->maxFileSize(15) // 15MB
    ->previewSize(500)
    ->editorTheme([
        'common.bi.image' => asset('images/default-banner.jpg'),
        'common.bisize.width' => '500px',
        'common.bisize.height' => '300px',
        'common.backgroundColor' => '#f8fafc',
        'common.border' => '2px dashed #cbd5e1',
        'header.backgroundImage' => 'none',
        'header.backgroundColor' => '#1f2937',
        'loadButton.backgroundColor' => '#3b82f6',
        'downloadButton.backgroundColor' => '#10b981',
        'menu.backgroundColor' => '#ffffff',
        'menu.borderColor' => '#e5e7eb',
        'submenu.backgroundColor' => '#f9fafb',
        'submenu.borderColor' => '#d1d5db',
    ])
    ->editorOptions([
        'cssMaxWidth' => 900,
        'cssMaxHeight' => 600,
        'usageStatistics' => false,
    ]);

// Contoh dalam form Filament
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Section::make('Informasi Dasar')
                ->schema([
                    TextInput::make('title')
                        ->label('Judul')
                        ->required(),
                    
                    RajaGambarUpload::make('featured_image')
                        ->label('Gambar Utama')
                        ->placeholder('Upload gambar utama')
                        ->directory('posts/featured')
                        ->maxFileSize(5)
                        ->previewSize(400)
                        ->enableEditor(true)
                        ->enableResize(true)
                        ->enableCrop(true)
                        ->enableFilter(true),
                ]),
                
            Section::make('Galeri')
                ->schema([
                    RajaGambarUpload::make('gallery_image_1')
                        ->label('Gambar Galeri 1')
                        ->directory('posts/gallery')
                        ->maxFileSize(3)
                        ->previewSize(250),
                        
                    RajaGambarUpload::make('gallery_image_2')
                        ->label('Gambar Galeri 2')
                        ->directory('posts/gallery')
                        ->maxFileSize(3)
                        ->previewSize(250),
                ]),
        ]);
}

/**
 * Konfigurasi yang tersedia:
 * 
 * acceptedFileTypes(array $types) - Tipe file yang diterima
 * maxFileSize(int $size) - Ukuran maksimal file dalam MB
 * directory(string $directory) - Direktori penyimpanan
 * visibility(string $visibility) - Visibilitas file (public/private)
 * placeholder(string $placeholder) - Placeholder text
 * previewSize(int $size) - Ukuran preview dalam pixel
 * 
 * Editor Features:
 * enableEditor(bool $enable) - Aktifkan/nonaktifkan editor
 * enableResize(bool $enable) - Aktifkan/nonaktifkan resize
 * enableCrop(bool $enable) - Aktifkan/nonaktifkan crop
 * enableRotate(bool $enable) - Aktifkan/nonaktifkan rotate
 * enableFlip(bool $enable) - Aktifkan/nonaktifkan flip
 * enableFilter(bool $enable) - Aktifkan/nonaktifkan filter
 * enableText(bool $enable) - Aktifkan/nonaktifkan text tool
 * enableDraw(bool $enable) - Aktifkan/nonaktifkan draw tool
 * enableShape(bool $enable) - Aktifkan/nonaktifkan shape tool
 * 
 * Customization:
 * editorTheme(array $theme) - Tema editor
 * editorOptions(array $options) - Opsi tambahan editor
 */
