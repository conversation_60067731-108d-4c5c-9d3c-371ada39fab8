<?php

namespace Modules\RajaGambar\Forms\Components;

use Filament\Forms\Components\Field;
use Illuminate\Support\Facades\Log;

class RajaGambarUpload extends Field
{
    protected string $view = 'rajagambar::forms.components.raja-gambar-upload';
    
    // Konfigurasi default
    protected array $acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    protected int $maxFileSize = 10; // MB
    protected string $directory = 'rajagambar';
    protected string $visibility = 'public';
    protected ?string $placeholder = null;
    protected int $previewSize = 300; // px
    protected bool $enableEditor = true;
    protected bool $enableResize = true;
    protected bool $enableCrop = true;
    protected bool $enableRotate = true;
    protected bool $enableFlip = true;
    protected bool $enableFilter = true;
    protected bool $enableText = true;
    protected bool $enableDraw = true;
    protected bool $enableShape = true;
    protected array $editorTheme = [];
    protected array $editorOptions = [];
    
    public static function make(string $name): static
    {
        $static = parent::make($name);

        return $static
            ->placeholder('Klik untuk upload gambar atau drag & drop')
            ->editorTheme([
                'common.bi.image' => asset('noimage.jpg'),
                'common.bisize.width' => '300px',
                'common.bisize.height' => '300px',
                'common.backgroundImage' => 'none',
                'common.backgroundColor' => '#f5f5f5',
                'common.border' => '1px solid #ddd',
            ]);
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Setup dehydration untuk memastikan data tersimpan dengan benar
        $this->dehydrateStateUsing(function ($state) {
            Log::info('RajaGambarUpload dehydrateStateUsing called', [
                'state' => $state,
                'state_type' => gettype($state),
                'field_name' => $this->getName()
            ]);

            // Jika state kosong, return null
            if (empty($state)) {
                Log::info('RajaGambarUpload dehydrateStateUsing: state empty, returning null');
                return null;
            }

            // Clean URL for storage (remove /storage/ prefix)
            if (is_string($state)) {
                $cleaned = $this->cleanUrlForStorage($state);
                Log::info('RajaGambarUpload dehydrateStateUsing: cleaned string', [
                    'original' => $state,
                    'cleaned' => $cleaned
                ]);
                return $cleaned;
            }

            // Jika state adalah array, ambil elemen pertama dan clean
            if (is_array($state) && count($state) > 0) {
                $cleaned = $this->cleanUrlForStorage($state[0]);
                Log::info('RajaGambarUpload dehydrateStateUsing: cleaned array', [
                    'original' => $state[0],
                    'cleaned' => $cleaned
                ]);
                return $cleaned;
            }

            Log::info('RajaGambarUpload dehydrateStateUsing: returning state as-is', ['state' => $state]);
            return $state;
        });

        // Setup hydration untuk memastikan data dimuat dengan benar
        $this->afterStateHydrated(function ($component, $state, $record) {
            // Jika state kosong tapi record ada, coba ambil dari database
            if (empty($state) && $record) {
                // Untuk field dengan dot notation seperti jcol.gambar
                $fieldName = $component->getName();
                if (strpos($fieldName, 'jcol.') === 0) {
                    $jsonKey = substr($fieldName, 5); // Hapus 'jcol.' dari key
                    if ($record->json && is_array($record->json)) {
                        $value = \Illuminate\Support\Arr::get($record->json, $jsonKey);
                        if ($value !== null) {
                            $component->state($value);
                        }
                    }
                }
            }
            // Pastikan state dalam format yang benar untuk RajaGambarUpload
            elseif ($state !== null && $state !== '') {
                $component->state($state);
            }
        });
    }
    
    public function acceptedFileTypes(array $types): static
    {
        $this->acceptedFileTypes = $types;
        return $this;
    }
    
    public function maxFileSize(int $size): static
    {
        $this->maxFileSize = $size;
        return $this;
    }
    
    public function directory(string $directory): static
    {
        $this->directory = $directory;
        return $this;
    }
    
    public function visibility(string $visibility): static
    {
        $this->visibility = $visibility;
        return $this;
    }
    
    public function placeholder(?string $placeholder): static
    {
        $this->placeholder = $placeholder;
        return $this;
    }
    
    public function previewSize(int $size): static
    {
        $this->previewSize = $size;
        return $this;
    }
    
    public function enableEditor(bool $enable = true): static
    {
        $this->enableEditor = $enable;
        return $this;
    }
    
    public function enableResize(bool $enable = true): static
    {
        $this->enableResize = $enable;
        return $this;
    }
    
    public function enableCrop(bool $enable = true): static
    {
        $this->enableCrop = $enable;
        return $this;
    }
    
    public function enableRotate(bool $enable = true): static
    {
        $this->enableRotate = $enable;
        return $this;
    }
    
    public function enableFlip(bool $enable = true): static
    {
        $this->enableFlip = $enable;
        return $this;
    }
    
    public function enableFilter(bool $enable = true): static
    {
        $this->enableFilter = $enable;
        return $this;
    }
    
    public function enableText(bool $enable = true): static
    {
        $this->enableText = $enable;
        return $this;
    }
    
    public function enableDraw(bool $enable = true): static
    {
        $this->enableDraw = $enable;
        return $this;
    }
    
    public function enableShape(bool $enable = true): static
    {
        $this->enableShape = $enable;
        return $this;
    }
    
    public function editorTheme(array $theme): static
    {
        $this->editorTheme = array_merge($this->editorTheme, $theme);
        return $this;
    }
    
    public function editorOptions(array $options): static
    {
        $this->editorOptions = array_merge($this->editorOptions, $options);
        return $this;
    }
    
    // Getter methods
    public function getAcceptedFileTypes(): array
    {
        return $this->acceptedFileTypes;
    }
    
    public function getAcceptedFileTypesString(): string
    {
        return implode(',', $this->acceptedFileTypes);
    }
    
    public function getMaxFileSize(): int
    {
        return $this->maxFileSize;
    }
    
    public function getDirectory(): string
    {
        return $this->directory;
    }
    
    public function getVisibility(): string
    {
        return $this->visibility;
    }
    
    public function getPlaceholder(): ?string
    {
        return $this->placeholder;
    }
    
    public function getPreviewSize(): int
    {
        return $this->previewSize;
    }
    
    public function isEditorEnabled(): bool
    {
        return $this->enableEditor;
    }
    
    public function isResizeEnabled(): bool
    {
        return $this->enableResize;
    }
    
    public function isCropEnabled(): bool
    {
        return $this->enableCrop;
    }
    
    public function isRotateEnabled(): bool
    {
        return $this->enableRotate;
    }
    
    public function isFlipEnabled(): bool
    {
        return $this->enableFlip;
    }
    
    public function isFilterEnabled(): bool
    {
        return $this->enableFilter;
    }
    
    public function isTextEnabled(): bool
    {
        return $this->enableText;
    }
    
    public function isDrawEnabled(): bool
    {
        return $this->enableDraw;
    }
    
    public function isShapeEnabled(): bool
    {
        return $this->enableShape;
    }
    
    public function getEditorTheme(): array
    {
        return $this->editorTheme;
    }
    
    public function getEditorOptions(): array
    {
        return $this->editorOptions;
    }

    /**
     * Clean URL for storage (remove /storage/ prefix and leading slash)
     */
    public function cleanUrlForStorage($url): ?string
    {
        if (empty($url) || !is_string($url)) {
            return $url;
        }

        // Remove /storage/ prefix if present
        if (str_starts_with($url, '/storage/')) {
            $cleanUrl = substr($url, 9); // Remove '/storage/' (9 characters)
            // Remove leading slash for cleaner storage
            if (str_starts_with($cleanUrl, '/')) {
                $cleanUrl = substr($cleanUrl, 1);
            }
            return $cleanUrl;
        }

        // Remove leading slash for cleaner storage
        if (str_starts_with($url, '/') && !str_starts_with($url, 'http')) {
            $url = substr($url, 1);
        }

        return $url;
    }
}
