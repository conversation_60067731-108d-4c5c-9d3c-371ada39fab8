<?php

namespace Modules\RajaGambar\Forms\Components;

use Filament\Forms\Components\Field;
use Filament\Forms\Set;
use Filament\Forms\Get;
use Illuminate\Support\Facades\Storage;
use Modules\RajaGambar\Services\ImageProcessingService;

class RajaGambarUpload extends Field
{
    protected string $view = 'rajagambar::forms.components.raja-gambar-upload';
    
    // Konfigurasi default
    protected array $acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    protected int $maxFileSize = 10; // MB
    protected string $directory = 'rajagambar';
    protected string $visibility = 'public';
    protected ?string $placeholder = null;
    protected int $previewSize = 300; // px
    protected bool $enableEditor = true;
    protected bool $enableResize = true;
    protected bool $enableCrop = true;
    protected bool $enableRotate = true;
    protected bool $enableFlip = true;
    protected bool $enableFilter = true;
    protected bool $enableText = true;
    protected bool $enableDraw = true;
    protected bool $enableShape = true;
    protected array $editorTheme = [];
    protected array $editorOptions = [];
    
    public static function make(string $name): static
    {
        $static = parent::make($name);
        
        return $static
            ->placeholder('Klik untuk upload gambar atau drag & drop')
            ->editorTheme([
                'common.bi.image' => asset('noimage.jpg'),
                'common.bisize.width' => '300px',
                'common.bisize.height' => '300px',
                'common.backgroundImage' => 'none',
                'common.backgroundColor' => '#f5f5f5',
                'common.border' => '1px solid #ddd',
            ]);
    }
    
    public function acceptedFileTypes(array $types): static
    {
        $this->acceptedFileTypes = $types;
        return $this;
    }
    
    public function maxFileSize(int $size): static
    {
        $this->maxFileSize = $size;
        return $this;
    }
    
    public function directory(string $directory): static
    {
        $this->directory = $directory;
        return $this;
    }
    
    public function visibility(string $visibility): static
    {
        $this->visibility = $visibility;
        return $this;
    }
    
    public function placeholder(?string $placeholder): static
    {
        $this->placeholder = $placeholder;
        return $this;
    }
    
    public function previewSize(int $size): static
    {
        $this->previewSize = $size;
        return $this;
    }
    
    public function enableEditor(bool $enable = true): static
    {
        $this->enableEditor = $enable;
        return $this;
    }
    
    public function enableResize(bool $enable = true): static
    {
        $this->enableResize = $enable;
        return $this;
    }
    
    public function enableCrop(bool $enable = true): static
    {
        $this->enableCrop = $enable;
        return $this;
    }
    
    public function enableRotate(bool $enable = true): static
    {
        $this->enableRotate = $enable;
        return $this;
    }
    
    public function enableFlip(bool $enable = true): static
    {
        $this->enableFlip = $enable;
        return $this;
    }
    
    public function enableFilter(bool $enable = true): static
    {
        $this->enableFilter = $enable;
        return $this;
    }
    
    public function enableText(bool $enable = true): static
    {
        $this->enableText = $enable;
        return $this;
    }
    
    public function enableDraw(bool $enable = true): static
    {
        $this->enableDraw = $enable;
        return $this;
    }
    
    public function enableShape(bool $enable = true): static
    {
        $this->enableShape = $enable;
        return $this;
    }
    
    public function editorTheme(array $theme): static
    {
        $this->editorTheme = array_merge($this->editorTheme, $theme);
        return $this;
    }
    
    public function editorOptions(array $options): static
    {
        $this->editorOptions = array_merge($this->editorOptions, $options);
        return $this;
    }
    
    // Getter methods
    public function getAcceptedFileTypes(): array
    {
        return $this->acceptedFileTypes;
    }
    
    public function getAcceptedFileTypesString(): string
    {
        return implode(',', $this->acceptedFileTypes);
    }
    
    public function getMaxFileSize(): int
    {
        return $this->maxFileSize;
    }
    
    public function getDirectory(): string
    {
        return $this->directory;
    }
    
    public function getVisibility(): string
    {
        return $this->visibility;
    }
    
    public function getPlaceholder(): ?string
    {
        return $this->placeholder;
    }
    
    public function getPreviewSize(): int
    {
        return $this->previewSize;
    }
    
    public function isEditorEnabled(): bool
    {
        return $this->enableEditor;
    }
    
    public function isResizeEnabled(): bool
    {
        return $this->enableResize;
    }
    
    public function isCropEnabled(): bool
    {
        return $this->enableCrop;
    }
    
    public function isRotateEnabled(): bool
    {
        return $this->enableRotate;
    }
    
    public function isFlipEnabled(): bool
    {
        return $this->enableFlip;
    }
    
    public function isFilterEnabled(): bool
    {
        return $this->enableFilter;
    }
    
    public function isTextEnabled(): bool
    {
        return $this->enableText;
    }
    
    public function isDrawEnabled(): bool
    {
        return $this->enableDraw;
    }
    
    public function isShapeEnabled(): bool
    {
        return $this->enableShape;
    }
    
    public function getEditorTheme(): array
    {
        return $this->editorTheme;
    }
    
    public function getEditorOptions(): array
    {
        return $this->editorOptions;
    }
}
