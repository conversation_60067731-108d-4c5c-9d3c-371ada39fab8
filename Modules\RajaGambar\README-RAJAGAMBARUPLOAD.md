# RajaGambarUpload Field Component

RajaGambarUpload adalah custom field component untuk FilamentPHP yang menyediakan fitur upload gambar dengan editor menggunakan TUI Image Editor. Komponen ini **tidak extends FileUpload** dan menggunakan [TUI Image Editor](https://github.com/nhn/tui.image-editor) sebagai image editor sekaligus image processing.

## Fitur Utama

- ✅ Upload gambar dengan drag & drop
- ✅ Editor gambar menggunakan TUI Image Editor
- ✅ Resize, crop, rotate, flip gambar
- ✅ Filter dan efek gambar
- ✅ Text dan drawing tools
- ✅ Shape tools
- ✅ Preview gambar real-time
- ✅ Integrasi penuh dengan Filament Forms
- ✅ Responsive design
- ✅ Custom theme dan styling
- ✅ API endpoint untuk upload dan processing

## Instalasi

Komponen ini sudah terinstall sebagai bagian dari modul RajaGambar. TUI Image Editor akan dimuat otomatis dari CDN saat diperlukan.

## Pengg<PERSON>an Dasar

```php
use Mo<PERSON>les\RajaGambar\Forms\Components\RajaGambarUpload;

// Pengg<PERSON>an sederhana
RajaGambarUpload::make('featured_image')
    ->label('Gambar Utama')
    ->placeholder('Upload gambar utama')
    ->directory('uploads/featured')
    ->maxFileSize(5); // 5MB
```

## Konfigurasi Lengkap

```php
RajaGambarUpload::make('profile_picture')
    ->label('Foto Profil')
    ->placeholder('Upload foto profil Anda')
    ->directory('profiles')
    ->visibility('public')
    ->acceptedFileTypes(['image/jpeg', 'image/png'])
    ->maxFileSize(2) // 2MB
    ->previewSize(300)
    ->enableEditor(true)
    ->enableResize(true)
    ->enableCrop(true)
    ->enableRotate(true)
    ->enableFlip(true)
    ->enableFilter(true)
    ->enableText(false) // Disable text tool
    ->enableDraw(false) // Disable draw tool
    ->enableShape(false) // Disable shape tool
    ->editorTheme([
        'common.backgroundColor' => '#ffffff',
        'common.border' => '1px solid #e5e7eb',
    ]);
```

## Method Konfigurasi

### File Upload Configuration

| Method | Parameter | Default | Deskripsi |
|--------|-----------|---------|-----------|
| `acceptedFileTypes()` | `array $types` | `['image/jpeg', 'image/png', 'image/webp', 'image/gif']` | Tipe file yang diterima |
| `maxFileSize()` | `int $size` | `10` | Ukuran maksimal file dalam MB |
| `directory()` | `string $directory` | `'rajagambar'` | Direktori penyimpanan |
| `visibility()` | `string $visibility` | `'public'` | Visibilitas file (public/private) |
| `placeholder()` | `string $placeholder` | `null` | Placeholder text |
| `previewSize()` | `int $size` | `300` | Ukuran preview dalam pixel |

### Editor Configuration

| Method | Parameter | Default | Deskripsi |
|--------|-----------|---------|-----------|
| `enableEditor()` | `bool $enable` | `true` | Aktifkan/nonaktifkan editor |
| `enableResize()` | `bool $enable` | `true` | Aktifkan/nonaktifkan resize |
| `enableCrop()` | `bool $enable` | `true` | Aktifkan/nonaktifkan crop |
| `enableRotate()` | `bool $enable` | `true` | Aktifkan/nonaktifkan rotate |
| `enableFlip()` | `bool $enable` | `true` | Aktifkan/nonaktifkan flip |
| `enableFilter()` | `bool $enable` | `true` | Aktifkan/nonaktifkan filter |
| `enableText()` | `bool $enable` | `true` | Aktifkan/nonaktifkan text tool |
| `enableDraw()` | `bool $enable` | `true` | Aktifkan/nonaktifkan draw tool |
| `enableShape()` | `bool $enable` | `true` | Aktifkan/nonaktifkan shape tool |

### Customization

| Method | Parameter | Default | Deskripsi |
|--------|-----------|---------|-----------|
| `editorTheme()` | `array $theme` | `[]` | Tema editor (lihat TUI Image Editor docs) |
| `editorOptions()` | `array $options` | `[]` | Opsi tambahan editor |

## Contoh Penggunaan dalam Form

```php
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Modules\RajaGambar\Forms\Components\RajaGambarUpload;

public function form(Form $form): Form
{
    return $form
        ->schema([
            Section::make('Media')
                ->schema([
                    RajaGambarUpload::make('featured_image')
                        ->label('Gambar Utama')
                        ->placeholder('Upload gambar utama artikel')
                        ->directory('articles/featured')
                        ->maxFileSize(5)
                        ->previewSize(400)
                        ->enableEditor(true)
                        ->enableResize(true)
                        ->enableCrop(true)
                        ->enableFilter(true),
                        
                    RajaGambarUpload::make('thumbnail')
                        ->label('Thumbnail')
                        ->placeholder('Upload thumbnail')
                        ->directory('articles/thumbnails')
                        ->maxFileSize(1)
                        ->previewSize(200)
                        ->enableEditor(true)
                        ->enableResize(true)
                        ->enableCrop(true)
                        ->enableRotate(false)
                        ->enableFlip(false)
                        ->enableFilter(false)
                        ->enableText(false)
                        ->enableDraw(false)
                        ->enableShape(false),
                ]),
        ]);
}
```

## Custom Theme

Anda dapat menyesuaikan tema TUI Image Editor:

```php
RajaGambarUpload::make('banner_image')
    ->editorTheme([
        'common.bi.image' => asset('images/default-banner.jpg'),
        'common.bisize.width' => '500px',
        'common.bisize.height' => '300px',
        'common.backgroundColor' => '#f8fafc',
        'common.border' => '2px dashed #cbd5e1',
        'header.backgroundImage' => 'none',
        'header.backgroundColor' => '#1f2937',
        'loadButton.backgroundColor' => '#3b82f6',
        'downloadButton.backgroundColor' => '#10b981',
        'menu.backgroundColor' => '#ffffff',
        'menu.borderColor' => '#e5e7eb',
        'submenu.backgroundColor' => '#f9fafb',
        'submenu.borderColor' => '#d1d5db',
    ])
    ->editorOptions([
        'cssMaxWidth' => 900,
        'cssMaxHeight' => 600,
        'usageStatistics' => false,
    ]);
```

## API Endpoints

Komponen ini menggunakan API endpoints berikut:

- `POST /api/rajagambar/upload` - Upload file
- `GET /api/rajagambar/file-info` - Informasi file
- `DELETE /api/rajagambar/delete` - Hapus file
- `POST /api/rajagambar/process-image` - Proses gambar

## Teknologi yang Digunakan

- **TUI Image Editor** - Editor gambar berbasis web
- **Fabric.js** - Library canvas untuk manipulasi gambar
- **Alpine.js** - Framework JavaScript reaktif
- **Filament Forms** - Form builder Laravel
- **Laravel Storage** - File storage system

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Troubleshooting

### Editor tidak muncul
- Pastikan koneksi internet stabil (TUI Image Editor dimuat dari CDN)
- Periksa console browser untuk error JavaScript

### Upload gagal
- Periksa ukuran file tidak melebihi batas
- Pastikan tipe file didukung
- Periksa permission direktori storage

### Gambar tidak tampil
- Pastikan symbolic link storage sudah dibuat: `php artisan storage:link`
- Periksa path gambar di database

## Lisensi

Komponen ini menggunakan TUI Image Editor yang berlisensi MIT. Lihat [dokumentasi TUI Image Editor](https://github.com/nhn/tui.image-editor) untuk detail lisensi.
