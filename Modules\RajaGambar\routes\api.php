<?php

use Illuminate\Support\Facades\Route;
use <PERSON><PERSON><PERSON>\RajaGambar\Http\Controllers\RajaGambarController;
use <PERSON><PERSON><PERSON>\RajaGambar\Http\Controllers\Api\ImageProcessingController;
use Mo<PERSON><PERSON>\RajaGambar\Http\Controllers\Api\RajaGambarFieldController;
use Modules\RajaGambar\Http\Controllers\Api\RajaGambarUploadController;

/*
 *--------------------------------------------------------------------------
 * API Routes
 *--------------------------------------------------------------------------
 *
 * Here is where you can register API routes for your application. These
 * routes are loaded by the RouteServiceProvider within a group which
 * is assigned the "api" middleware group. Enjoy building your API!
 *
*/

Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    Route::apiResource('rajagambar', RajaGambarController::class)->names('rajagambar');

    // Image Processing Routes
    Route::prefix('image-processing')->group(function () {
        Route::post('info', [ImageProcessingController::class, 'getImageInfo']);
        Route::post('responsive-sizes', [ImageProcessingController::class, 'createResponsiveSizes']);
        Route::post('optimize', [ImageProcessingController::class, 'optimizeForWeb']);
        Route::post('process', [ImageProcessingController::class, 'processImage']);
        Route::post('watermark', [ImageProcessingController::class, 'addWatermark']);
        Route::post('batch-process', [ImageProcessingController::class, 'batchProcess']);
        Route::post('cleanup', [ImageProcessingController::class, 'cleanup']);
    });

    // RajaGambar Field API Routes
    Route::prefix('field')->group(function () {
        Route::post('image-info', [RajaGambarFieldController::class, 'getImageInfo']);
        Route::post('generate-responsive', [RajaGambarFieldController::class, 'generateResponsive']);
        Route::post('apply-effect', [RajaGambarFieldController::class, 'applyEffect']);
        Route::post('apply-watermark', [RajaGambarFieldController::class, 'applyWatermark']);
        Route::post('processing-history', [RajaGambarFieldController::class, 'getProcessingHistory']);
    });
});

// Public API routes for RajaGambarUpload (no auth required for file operations)
Route::prefix('rajagambar')->group(function () {
    Route::post('upload', [RajaGambarUploadController::class, 'upload']);
    Route::get('file-info', [RajaGambarUploadController::class, 'fileInfo']);
    Route::delete('delete', [RajaGambarUploadController::class, 'delete']);
    Route::post('process-image', [RajaGambarUploadController::class, 'processImage']);
});
