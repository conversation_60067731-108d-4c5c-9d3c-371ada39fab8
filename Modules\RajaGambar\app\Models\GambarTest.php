<?php

namespace Modules\RajaGambar\app\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GambarTest extends Model
{
    use HasFactory;

    protected $table = 'gambar_tests';

    protected $fillable = [
        'title',
        'featured_image',
        'description',
    ];

    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
}
