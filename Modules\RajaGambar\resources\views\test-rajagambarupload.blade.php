<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Test RajaGambarUpload Component</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Filament Styles (minimal) -->
    <style>
        .fi-fo-field-wrp {
            margin-bottom: 1rem;
        }
        
        .fi-fo-field-wrp-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .fi-fo-field-wrp-hint {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Test RajaGambarUpload Component</h1>
            
            <form x-data="testForm()" @submit.prevent="submitForm()">
                <div class="space-y-6">
                    <!-- Test Field 1: Basic Usage -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <h3 class="text-lg font-semibold mb-4">Basic Usage</h3>
                        
                        <div class="fi-fo-field-wrp">
                            <label class="fi-fo-field-wrp-label">Gambar Utama</label>
                            
                            <div class="raja-gambar-upload-container" x-data="rajaGambarUpload({
                                statePath: 'featured_image',
                                placeholder: 'Upload gambar utama',
                                previewSize: 300,
                                maxFileSize: 5,
                                acceptedTypes: 'image/jpeg,image/png,image/webp,image/gif',
                                directory: 'test/featured',
                                visibility: 'public',
                                editorTheme: {
                                    'common.backgroundColor': '#f5f5f5',
                                    'common.border': '1px solid #ddd'
                                },
                                editorOptions: {},
                                enableEditor: true,
                                enableResize: true,
                                enableCrop: true,
                                enableRotate: true,
                                enableFlip: true,
                                enableFilter: true,
                                enableText: true,
                                enableDraw: true,
                                enableShape: true,
                                currentValue: null
                            })">
                                
                                <!-- Hidden input untuk menyimpan nilai -->
                                <input
                                    type="hidden"
                                    name="featured_image"
                                    x-model="value"
                                />
                                
                                <!-- Upload Area -->
                                <div class="raja-gambar-upload-area" x-show="!hasImage">
                                    <div 
                                        class="upload-dropzone"
                                        @click="triggerFileInput()"
                                        @dragover.prevent="dragOver = true"
                                        @dragleave.prevent="dragOver = false"
                                        @drop.prevent="handleFileDrop($event)"
                                        :class="{ 'drag-over': dragOver }"
                                    >
                                        <div class="upload-content">
                                            <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                            </svg>
                                            <p class="upload-text" x-text="placeholder"></p>
                                            <p class="upload-hint">Atau drag & drop gambar di sini</p>
                                            <p class="upload-info">Maksimal 5MB - Format: jpeg, png, webp, gif</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Hidden file input -->
                                    <input 
                                        type="file" 
                                        x-ref="fileInput"
                                        @change="handleFileSelect($event)"
                                        accept="image/jpeg,image/png,image/webp,image/gif"
                                        class="hidden"
                                    />
                                </div>
                                
                                <!-- Preview & Editor Area -->
                                <div class="raja-gambar-preview-area" x-show="hasImage">
                                    <!-- Current Image Preview -->
                                    <div class="current-image-preview" x-show="!showEditor">
                                        <div class="image-container">
                                            <img 
                                                x-ref="previewImage"
                                                :src="imagePreviewUrl" 
                                                :alt="fileName"
                                                :style="`max-width: ${previewSize}px; max-height: ${previewSize}px;`"
                                                class="preview-image"
                                            />
                                        </div>
                                        
                                        <div class="image-actions">
                                            <button 
                                                type="button"
                                                @click="openEditor()"
                                                class="btn-edit"
                                                x-show="enableEditor"
                                            >
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Edit Gambar
                                            </button>
                                            
                                            <button 
                                                type="button"
                                                @click="removeImage()"
                                                class="btn-remove"
                                            >
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                                Hapus
                                            </button>
                                        </div>
                                        
                                        <div class="image-info" x-show="fileName">
                                            <p class="file-name" x-text="fileName"></p>
                                            <p class="file-size" x-text="fileSize"></p>
                                        </div>
                                    </div>
                                    
                                    <!-- TUI Image Editor -->
                                    <div class="tui-image-editor-container" x-show="showEditor">
                                        <div class="editor-header">
                                            <h3 class="editor-title">Edit Gambar</h3>
                                            <div class="editor-actions">
                                                <button 
                                                    type="button"
                                                    @click="saveEditedImage()"
                                                    class="btn-save"
                                                >
                                                    Simpan
                                                </button>
                                                <button 
                                                    type="button"
                                                    @click="cancelEdit()"
                                                    class="btn-cancel"
                                                >
                                                    Batal
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <div 
                                            x-ref="tuiImageEditor"
                                            class="tui-image-editor"
                                        ></div>
                                    </div>
                                </div>
                                
                                <!-- Loading State -->
                                <div class="loading-overlay" x-show="loading">
                                    <div class="loading-spinner">
                                        <svg class="animate-spin h-8 w-8" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <p>Memproses gambar...</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="fi-fo-field-wrp-hint">Upload gambar dengan format JPEG, PNG, WebP, atau GIF. Maksimal 5MB.</div>
                        </div>
                    </div>
                    
                    <!-- Debug Info -->
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <h3 class="text-lg font-semibold mb-4">Debug Info</h3>
                        <div class="space-y-2 text-sm">
                            <div><strong>Featured Image Value:</strong> <span x-text="JSON.stringify(formData.featured_image)"></span></div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button 
                            type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            Submit Form
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Include the component styles and scripts manually -->
    <style>
    .raja-gambar-upload-container {
      position: relative;
    }

    .raja-gambar-upload-area {
      margin-bottom: 1rem;
    }

    .upload-dropzone {
      border: 2px dashed #d1d5db;
      border-radius: 0.5rem;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
      background-color: #f9fafb;
    }

    .upload-dropzone:hover,
    .upload-dropzone.drag-over {
      border-color: #3b82f6;
      background-color: #eff6ff;
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .upload-icon {
      width: 3rem;
      height: 3rem;
      color: #6b7280;
    }

    .upload-text {
      font-size: 1.125rem;
      font-weight: 500;
      color: #374151;
      margin: 0;
    }

    .upload-hint {
      font-size: 0.875rem;
      color: #6b7280;
      margin: 0;
    }

    .upload-info {
      font-size: 0.75rem;
      color: #9ca3af;
      margin: 0;
    }

    .raja-gambar-preview-area {
      margin-bottom: 1rem;
    }

    .current-image-preview {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }

    .image-container {
      position: relative;
      border-radius: 0.5rem;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .preview-image {
      display: block;
      border-radius: 0.5rem;
      object-fit: cover;
    }

    .image-actions {
      display: flex;
      gap: 0.5rem;
    }

    .btn-edit,
    .btn-remove,
    .btn-save,
    .btn-cancel {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      font-weight: 500;
      transition: all 0.2s ease-in-out;
      border: none;
      cursor: pointer;
    }

    .btn-edit {
      background-color: #3b82f6;
      color: white;
    }

    .btn-edit:hover {
      background-color: #2563eb;
    }

    .btn-remove {
      background-color: #ef4444;
      color: white;
    }

    .btn-remove:hover {
      background-color: #dc2626;
    }

    .btn-save {
      background-color: #10b981;
      color: white;
    }

    .btn-save:hover {
      background-color: #059669;
    }

    .btn-cancel {
      background-color: #6b7280;
      color: white;
    }

    .btn-cancel:hover {
      background-color: #4b5563;
    }

    .image-info {
      text-align: center;
    }

    .file-name {
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
      margin: 0 0 0.25rem 0;
    }

    .file-size {
      font-size: 0.75rem;
      color: #6b7280;
      margin: 0;
    }

    .tui-image-editor-container {
      border: 1px solid #d1d5db;
      border-radius: 0.5rem;
      overflow: hidden;
      background-color: white;
    }

    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      border-bottom: 1px solid #e5e7eb;
      background-color: #f9fafb;
    }

    .editor-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #374151;
      margin: 0;
    }

    .editor-actions {
      display: flex;
      gap: 0.5rem;
    }

    .tui-image-editor {
      height: 500px;
    }

    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0.5rem;
      z-index: 10;
    }

    .loading-spinner {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
      color: #3b82f6;
    }

    .loading-spinner p {
      font-size: 0.875rem;
      font-weight: 500;
      margin: 0;
    }

    /* TUI Image Editor custom styles */
    .tui-image-editor-canvas-container {
      background-color: #f5f5f5 !important;
    }

    .tui-image-editor-menu {
      background-color: #ffffff !important;
      border-bottom: 1px solid #e5e7eb !important;
    }

    .tui-image-editor-submenu {
      background-color: #f9fafb !important;
      border-bottom: 1px solid #e5e7eb !important;
    }
    </style>
    
    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.data('testForm', () => ({
                formData: {
                    featured_image: null
                },

                init() {
                    // Watch for changes in form data
                    this.$watch('formData', (newData) => {
                        console.log('Form data changed:', newData);
                    });
                },

                submitForm() {
                    console.log('Submitting form with data:', this.formData);
                    alert('Form submitted! Check console for data.');
                }
            }));

            // RajaGambarUpload Alpine component
            Alpine.data('rajaGambarUpload', (config) => ({
                // Configuration
                statePath: config.statePath || '',
                placeholder: config.placeholder || 'Upload gambar',
                previewSize: config.previewSize || 200,
                maxFileSize: config.maxFileSize || 2,
                acceptedTypes: config.acceptedTypes || 'image/jpeg,image/png',
                directory: config.directory || 'uploads',
                visibility: config.visibility || 'public',
                editorTheme: config.editorTheme || {},
                editorOptions: config.editorOptions || {},
                enableEditor: config.enableEditor !== false,
                enableResize: config.enableResize !== false,
                enableCrop: config.enableCrop !== false,
                enableRotate: config.enableRotate !== false,
                enableFlip: config.enableFlip !== false,
                enableFilter: config.enableFilter !== false,
                enableText: config.enableText !== false,
                enableDraw: config.enableDraw !== false,
                enableShape: config.enableShape !== false,
                currentValue: config.currentValue || null,

                // State
                value: null,
                hasImage: false,
                imagePreviewUrl: null,
                fileName: null,
                fileSize: null,
                loading: false,
                dragOver: false,
                showEditor: false,
                tuiEditor: null,
                originalImageData: null,

                init() {
                    // Initialize value
                    this.value = this.currentValue;
                    this.hasImage = !!this.value;

                    if (this.hasImage && this.value) {
                        this.loadExistingImage();
                    }

                    // Watch for value changes
                    this.$watch('value', (newValue) => {
                        // Update parent form data if available
                        if (this.$root && this.$root.formData) {
                            this.$root.formData[this.statePath] = newValue;
                        }
                    });
                },

                triggerFileInput() {
                    this.$refs.fileInput.click();
                },

                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (file) {
                        this.processFile(file);
                    }
                },

                handleFileDrop(event) {
                    this.dragOver = false;
                    const file = event.dataTransfer.files[0];
                    if (file) {
                        this.processFile(file);
                    }
                },

                async processFile(file) {
                    // Validate file type
                    if (!this.acceptedTypes.split(',').some(type => file.type === type.trim())) {
                        alert('Tipe file tidak didukung. Gunakan: ' + this.acceptedTypes);
                        return;
                    }

                    // Validate file size (in MB)
                    if (file.size > this.maxFileSize * 1024 * 1024) {
                        alert(`Ukuran file terlalu besar. Maksimal ${this.maxFileSize}MB`);
                        return;
                    }

                    this.loading = true;

                    try {
                        // Upload file
                        const formData = new FormData();
                        formData.append('file', file);
                        formData.append('directory', this.directory);
                        formData.append('visibility', this.visibility);

                        const response = await fetch('/api/rajagambar/upload', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            this.value = result.data.path;
                            this.hasImage = true;
                            this.imagePreviewUrl = result.data.url;
                            this.fileName = result.data.name;
                            this.fileSize = this.formatFileSize(result.data.size);

                            // Store original image data for editor
                            this.originalImageData = result.data;
                        } else {
                            alert('Gagal upload gambar: ' + (result.message || 'Unknown error'));
                        }
                    } catch (error) {
                        console.error('Upload error:', error);
                        alert('Terjadi kesalahan saat upload gambar');
                    } finally {
                        this.loading = false;
                    }
                },

                async loadExistingImage() {
                    try {
                        const response = await fetch(`/api/rajagambar/file-info?path=${encodeURIComponent(this.value)}`);
                        const result = await response.json();

                        if (result.success) {
                            this.imagePreviewUrl = result.data.url;
                            this.fileName = result.data.name;
                            this.fileSize = this.formatFileSize(result.data.size);
                            this.originalImageData = result.data;
                        }
                    } catch (error) {
                        console.error('Load image error:', error);
                    }
                },

                async removeImage() {
                    if (!confirm('Apakah Anda yakin ingin menghapus gambar ini?')) {
                        return;
                    }

                    this.loading = true;

                    try {
                        if (this.value) {
                            const response = await fetch('/api/rajagambar/delete', {
                                method: 'DELETE',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                },
                                body: JSON.stringify({ path: this.value })
                            });

                            const result = await response.json();
                            if (!result.success) {
                                console.warn('Failed to delete file:', result.message);
                            }
                        }

                        // Reset state
                        this.value = null;
                        this.hasImage = false;
                        this.imagePreviewUrl = null;
                        this.fileName = null;
                        this.fileSize = null;
                        this.originalImageData = null;
                        this.showEditor = false;

                        // Clear file input
                        this.$refs.fileInput.value = '';

                    } catch (error) {
                        console.error('Remove image error:', error);
                        alert('Terjadi kesalahan saat menghapus gambar');
                    } finally {
                        this.loading = false;
                    }
                },

                async openEditor() {
                    if (!this.enableEditor || !this.imagePreviewUrl) return;

                    this.showEditor = true;

                    // Wait for DOM to update
                    await this.$nextTick();

                    // Load TUI Image Editor if not already loaded
                    if (!window.tui || !window.tui.ImageEditor) {
                        await this.loadTUIImageEditor();
                    }

                    // Initialize editor
                    this.initializeTUIEditor();
                },

                async loadTUIImageEditor() {
                    return new Promise((resolve, reject) => {
                        // Load Fabric.js first
                        if (!window.fabric) {
                            const fabricScript = document.createElement('script');
                            fabricScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js';
                            fabricScript.onload = () => {
                                // Then load TUI Image Editor
                                const tuiScript = document.createElement('script');
                                tuiScript.src = 'https://uicdn.toast.com/tui-image-editor/latest/tui-image-editor.js';
                                tuiScript.onload = () => {
                                    // Load CSS
                                    const tuiCSS = document.createElement('link');
                                    tuiCSS.rel = 'stylesheet';
                                    tuiCSS.href = 'https://uicdn.toast.com/tui-image-editor/latest/tui-image-editor.css';
                                    document.head.appendChild(tuiCSS);
                                    resolve();
                                };
                                tuiScript.onerror = reject;
                                document.head.appendChild(tuiScript);
                            };
                            fabricScript.onerror = reject;
                            document.head.appendChild(fabricScript);
                        } else if (!window.tui || !window.tui.ImageEditor) {
                            const tuiScript = document.createElement('script');
                            tuiScript.src = 'https://uicdn.toast.com/tui-image-editor/latest/tui-image-editor.js';
                            tuiScript.onload = resolve;
                            tuiScript.onerror = reject;
                            document.head.appendChild(tuiScript);
                        } else {
                            resolve();
                        }
                    });
                },

                initializeTUIEditor() {
                    if (this.tuiEditor) {
                        this.tuiEditor.destroy();
                    }

                    const editorConfig = {
                        includeUI: {
                            loadImage: {
                                path: this.imagePreviewUrl,
                                name: this.fileName || 'image'
                            },
                            theme: this.editorTheme,
                            menu: this.getMenuConfig(),
                            initMenu: 'filter',
                            uiSize: {
                                width: '100%',
                                height: '500px'
                            },
                            menuBarPosition: 'bottom'
                        },
                        cssMaxWidth: 700,
                        cssMaxHeight: 500,
                        usageStatistics: false,
                        ...this.editorOptions
                    };

                    this.tuiEditor = new tui.ImageEditor(this.$refs.tuiImageEditor, editorConfig);
                },

                getMenuConfig() {
                    const menu = [];

                    if (this.enableResize) menu.push('resize');
                    if (this.enableCrop) menu.push('crop');
                    if (this.enableFlip) menu.push('flip');
                    if (this.enableRotate) menu.push('rotate');
                    if (this.enableFilter) menu.push('filter');
                    if (this.enableText) menu.push('text');
                    if (this.enableDraw) menu.push('draw');
                    if (this.enableShape) menu.push('shape');

                    return menu;
                },

                async saveEditedImage() {
                    if (!this.tuiEditor) return;

                    this.loading = true;

                    try {
                        // Get edited image as data URL
                        const imageDataURL = this.tuiEditor.toDataURL();

                        // Convert to blob
                        const response = await fetch(imageDataURL);
                        const blob = await response.blob();

                        // Create form data
                        const formData = new FormData();
                        formData.append('image', blob, this.fileName || 'edited-image.png');
                        formData.append('directory', this.directory);
                        formData.append('visibility', this.visibility);
                        formData.append('original_path', this.value);

                        // Process image
                        const processResponse = await fetch('/api/rajagambar/process-image', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            }
                        });

                        const result = await processResponse.json();

                        if (result.success) {
                            // Update with new image data
                            this.value = result.data.path;
                            this.imagePreviewUrl = result.data.url;
                            this.fileName = result.data.name;
                            this.fileSize = this.formatFileSize(result.data.size);
                            this.originalImageData = result.data;

                            // Close editor
                            this.cancelEdit();

                            alert('Gambar berhasil disimpan!');
                        } else {
                            alert('Gagal menyimpan gambar: ' + (result.message || 'Unknown error'));
                        }
                    } catch (error) {
                        console.error('Save edited image error:', error);
                        alert('Terjadi kesalahan saat menyimpan gambar');
                    } finally {
                        this.loading = false;
                    }
                },

                cancelEdit() {
                    this.showEditor = false;
                    if (this.tuiEditor) {
                        this.tuiEditor.destroy();
                        this.tuiEditor = null;
                    }
                },

                formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            }));
        });
    </script>
</body>
</html>
